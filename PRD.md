# drivly - Drivers Performance, Income Tracking and Spare Parts Monitoring App
## Comprehensive Product Requirements Document (PRD)

### Executive Summary

**drivly** is a comprehensive Flutter application designed for drivers to track their performance, income, and spare parts maintenance. The application provides detailed analytics, cloud synchronization, and comprehensive data management capabilities for professional drivers.

### Application Overview

- **Name**: drivly
- **Platform**: Flutter (Cross-platform)
- **Target Users**: Professional drivers (ride-sharing, delivery, taxi)
- **Core Purpose**: Performance tracking, income monitoring, spare parts management
- **Architecture**: Clean Architecture with feature-based organization

## 1. Technical Architecture

### 1.1 Core Technology Stack

#### Frontend Framework
- **Flutter SDK**: Cross-platform mobile development
- **Material Design 3**: Modern UI design system
- **Google Fonts**: Typography (Poppins font family)

#### State Management
- **Riverpod Ecosystem**:
  - `flutter_riverpod: ^2.3.6`
  - `hooks_riverpod: ^2.3.6`
  - `riverpod_annotation: ^2.1.1`
  - `riverpod_generator: ^2.2.3`
- **Flutter Hooks**: `flutter_hooks: ^0.20.0`

#### Database & Persistence
- **Drift ORM**: `drift: ^2.10.0` (Type-safe SQL database)
- **SQLite**: Local database storage
- **Shared Preferences**: Simple key-value storage
- **Path Provider**: File system access

#### Cloud Integration & Sync
- **Supabase**: `supabase_flutter: ^1.10.25`
  - Authentication
  - Real-time database
  - Cloud storage
- **Connectivity Plus**: Network connectivity monitoring

#### Code Generation
- **Build Runner**: `build_runner: ^2.4.6`
- **Freezed**: `freezed: ^2.3.5` (Immutable data classes)
- **JSON Serializable**: `json_serializable: ^6.7.0`
- **Drift Dev**: `drift_dev: ^2.10.0` (Database code generation)

#### UI & Visualization
- **FL Chart**: `fl_chart: ^0.65.0` (Charts and graphs)
- **Shimmer**: `shimmer: ^3.0.0` (Loading animations)
- **Flutter SVG**: `flutter_svg: ^2.0.7` (Vector graphics)

#### Utilities
- **UUID**: `uuid: ^4.2.2` (Unique identifiers)
- **Intl**: `intl: ^0.18.1` (Internationalization)
- **File Picker**: `file_picker: ^9.1.0`
- **Permission Handler**: `permission_handler: ^11.0.1`

### 1.2 Application Architecture

#### Clean Architecture Pattern
```
lib/
├── core/                    # Core functionality
│   ├── config/             # Configuration
│   ├── datasources/        # Database definitions
│   ├── errors/             # Error handling
│   ├── models/             # Shared models
│   ├── providers/          # Global providers
│   ├── repositories/       # Base repository patterns
│   ├── services/           # Core services
│   ├── theme/              # Design system
│   ├── utils/              # Utilities
│   └── components/         # Reusable UI components
├── features/               # Feature modules
│   ├── auth/              # Authentication
│   ├── backup/            # Backup & restore
│   ├── home/              # Dashboard
│   ├── income/            # Income tracking
│   ├── levels/            # Driver levels
│   ├── more/              # Settings & more
│   ├── orders/            # Order management
│   ├── performance/       # Performance tracking
│   ├── settings/          # App settings
│   ├── spare_parts/       # Spare parts management
│   └── sync/              # Data synchronization
└── main.dart              # Application entry point
```

#### Feature Module Structure
Each feature follows clean architecture:
```
feature/
├── data/
│   ├── datasources/       # Data sources
│   ├── models/            # Data models
│   └── repositories/      # Repository implementations
├── domain/
│   ├── entities/          # Domain entities
│   ├── repositories/      # Repository interfaces
│   └── usecases/          # Business logic
└── presentation/
    ├── providers/         # State management
    ├── screens/           # UI screens
    └── widgets/           # UI components
```

### 1.3 Database Schema

#### Core Tables

**Income Table**
```sql
CREATE TABLE income (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  date DATETIME,
  initial_mileage INTEGER,
  final_mileage INTEGER,
  initial_gopay REAL,
  initial_bca REAL,
  initial_cash REAL,
  initial_ovo REAL,
  initial_bri REAL,
  initial_rekpon REAL,
  final_gopay REAL,
  final_bca REAL,
  final_cash REAL,
  final_ovo REAL,
  final_bri REAL,
  final_rekpon REAL,
  initial_capital REAL,
  final_result REAL,
  mileage INTEGER,        -- Computed: final - initial mileage
  net_income REAL,        -- Computed: total final - total initial
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

**Orders Table**
```sql
CREATE TABLE orders (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  date DATETIME,
  order_completed INTEGER,
  order_missed INTEGER,
  order_canceled INTEGER,
  cbs_order INTEGER,
  incoming_order INTEGER,     -- Computed field
  order_received INTEGER,     -- Computed field
  bid_acceptance REAL,        -- Computed field
  trip_completion REAL,       -- Computed field
  points INTEGER,
  trip REAL,
  bonus REAL,
  tips REAL,
  income REAL,               -- Computed field
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

**Performance Table**
```sql
CREATE TABLE performance (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  date DATETIME,
  bid_performance REAL,
  trip_performance REAL,
  active_days INTEGER,
  online_hours REAL,
  avg_completed REAL,        -- Computed field
  avg_online REAL,           -- Computed field
  retention REAL,            -- Computed field
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

**Spare Parts Table**
```sql
CREATE TABLE spare_parts (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  part_name TEXT,
  part_type TEXT,
  price REAL,
  mileage_limit INTEGER,
  initial_mileage INTEGER,
  installation_date DATETIME,
  current_mileage INTEGER DEFAULT 0,
  warning_status BOOLEAN DEFAULT FALSE,
  replacement_count INTEGER DEFAULT 0,
  notes TEXT DEFAULT '',
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

**Spare Parts History Table**
```sql
CREATE TABLE spare_parts_history (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  spare_part_id INTEGER,
  action_type TEXT,          -- 'installed', 'replaced', 'removed'
  action_date DATETIME,
  mileage_at_action INTEGER,
  cost REAL,
  notes TEXT,
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

**Level Settings Table**
```sql
CREATE TABLE level_settings (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  level_name TEXT,
  min_bid_performance REAL,
  min_trip_performance REAL,
  min_active_days INTEGER,
  min_online_hours REAL,
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

**App Settings Table**
```sql
CREATE TABLE app_settings (
  uuid TEXT PRIMARY KEY,
  id INTEGER AUTOINCREMENT,
  setting_key TEXT UNIQUE,
  setting_value TEXT,
  created_at DATETIME,
  updated_at DATETIME,
  deleted_at DATETIME,
  sync_status TEXT DEFAULT 'pendingUpload'
);
```

#### Sync Status Enum
```dart
enum SyncStatus {
  pendingUpload,    // Needs to be uploaded to cloud
  synced,           // Successfully synced
  conflict,         // Sync conflict detected
  error            // Sync error occurred
}
```

## 2. Feature Specifications

### 2.1 Authentication System

#### Authentication Flow
- **Optional Authentication**: App works offline without authentication
- **Supabase Integration**: Email/password authentication
- **Session Management**: Persistent login sessions
- **Guest Mode**: Full functionality without account

#### Implementation Details
- **AuthWrapper**: Root authentication wrapper
- **AuthStateManager**: Centralized auth state management
- **AuthRepository**: Authentication operations
- **AuthLogger**: Detailed authentication logging

### 2.2 Income Tracking Module

#### Core Features
- **Daily Income Recording**: Track multiple payment methods
- **Payment Methods Supported**:
  - GoPay (e-wallet)
  - BCA (bank)
  - Cash
  - OVO (e-wallet)
  - BRI (bank)
  - Rekpon (payment method)

#### Data Model
```dart
class Income {
  int? id;
  DateTime date;
  int initialMileage;
  int finalMileage;
  double initialGopay;
  double initialBca;
  double initialCash;
  double initialOvo;
  double initialBri;
  double initialRekpon;
  double finalGopay;
  double finalBca;
  double finalCash;
  double finalOvo;
  double finalBri;
  double finalRekpon;
  double? initialCapital;    // Computed
  double? finalResult;       // Computed
  int? mileage;             // Computed: final - initial
  double? netIncome;        // Computed: total final - total initial
}
```

#### UI Components
- **Income Form**: Multi-field input form
- **Income List**: Historical income records
- **Income Trends Chart**: Line chart visualization
- **Income Summary Cards**: Daily/weekly/monthly summaries

### 2.3 Orders Management Module

#### Core Features
- **Order Tracking**: Track completed, missed, canceled orders
- **Performance Metrics**: Bid acceptance, trip completion rates
- **Points System**: Driver points tracking
- **Revenue Breakdown**: Trip fees, bonuses, tips

#### Data Model
```dart
class Order {
  int? id;
  DateTime date;
  int orderCompleted;
  int orderMissed;
  int orderCanceled;
  int cbsOrder;
  int? incomingOrder;       // Computed
  int? orderReceived;       // Computed
  double? bidAcceptance;    // Computed percentage
  double? tripCompletion;   // Computed percentage
  int points;
  double trip;
  double bonus;
  double tips;
  double? income;           // Computed total
}
```

#### UI Components
- **Order Form**: Daily order entry form
- **Order Trends Chart**: Performance visualization
- **Order Summary Cards**: Key metrics display
- **Order History List**: Historical records

### 2.4 Performance Tracking Module

#### Core Features
- **Performance Metrics**: Bid and trip performance tracking
- **Activity Monitoring**: Active days and online hours
- **Performance Analytics**: Averages and retention rates
- **Level Assessment**: Driver level evaluation

#### Data Model
```dart
class Performance {
  int? id;
  DateTime date;
  double bidPerformance;
  double tripPerformance;
  int activeDays;
  double onlineHours;
  double? avgCompleted;     // Computed average
  double? avgOnline;        // Computed average
  double? retention;        // Computed retention rate
}
```

#### UI Components
- **Performance Form**: Performance data entry
- **Performance Charts**: Trend visualization
- **Performance Summary**: Key metrics overview
- **Level Progress**: Driver level progression

### 2.5 Spare Parts Management Module

#### Core Features
- **Parts Inventory**: Track spare parts and maintenance
- **Mileage Monitoring**: Usage-based maintenance alerts
- **Replacement History**: Complete maintenance log
- **Cost Tracking**: Maintenance expense monitoring
- **Warning System**: Proactive maintenance alerts

#### Data Model
```dart
class EnhancedSparePart {
  int? id;
  String partName;
  String partType;
  double price;
  int mileageLimit;
  int initialMileage;
  DateTime installationDate;
  int currentMileage;
  bool warningStatus;
  int replacementCount;
  String notes;
  DateTime createdAt;
  DateTime updatedAt;
  // Computed fields
  int usageMileage;         // currentMileage - initialMileage
  int daysInUse;           // days since installation
  double usagePercent;     // (usageMileage / mileageLimit) * 100
}
```

#### UI Components
- **Parts List**: Inventory overview with status indicators
- **Part Detail**: Individual part information and history
- **Add/Edit Part Form**: Part management interface
- **Maintenance Alerts**: Warning notifications
- **Parts History**: Complete replacement log

### 2.6 Driver Levels System

#### Core Features
- **Level Requirements**: Configurable performance thresholds
- **Level Assessment**: Automatic level evaluation
- **Progress Tracking**: Visual progress indicators
- **Achievement System**: Level-based achievements

#### Data Model
```dart
class LevelRequirements {
  String levelName;
  double minBidPerformance;
  double minTripPerformance;
  int minActiveDays;
  double minOnlineHours;
}
```

#### UI Components
- **Level Overview**: Current level and progress
- **Requirements Display**: Level criteria visualization
- **Progress Charts**: Performance vs requirements
- **Achievement Badges**: Level completion indicators

## 3. Synchronization System

### 3.1 Sync Architecture

#### Core Components
- **SyncService**: Main synchronization orchestrator
- **SyncOperations**: Handles upload/download operations
- **SyncQueue**: Manages pending sync operations
- **ConnectivityMonitor**: Network connectivity monitoring
- **SyncLogger**: Comprehensive sync logging

#### Sync Operations
```dart
enum SyncOperation {
  upload,    // Upload local changes to cloud
  download,  // Download remote changes to local
  full      // Complete bidirectional sync
}
```

#### Sync Status Management
```dart
enum SyncStatus {
  pendingUpload,  // Local changes need upload
  synced,         // Successfully synchronized
  conflict,       // Sync conflict detected
  error          // Sync operation failed
}
```

### 3.2 Conflict Resolution

#### Strategy
- **UUID-based Identification**: Each record has unique UUID
- **Timestamp Comparison**: Last-modified wins approach
- **Upsert Operations**: Use Supabase upsert with UUID conflict resolution
- **Soft Delete**: Maintain deleted_at timestamp for sync

#### Implementation
```dart
// Supabase upsert with conflict resolution
await supabase.client
  .from(tableName)
  .upsert(jsonData, onConflict: 'uuid');
```

### 3.3 Offline Support

#### Features
- **Full Offline Functionality**: App works without internet
- **Local Data Storage**: SQLite database for offline data
- **Sync Queue**: Queues operations for later sync
- **Connectivity Monitoring**: Automatic sync when online
- **Debounced Sync**: Prevents excessive sync operations

#### Sync Triggers
- **Manual Sync**: User-initiated synchronization
- **Scheduled Sync**: Time-based automatic sync
- **Connectivity Sync**: Sync when connection restored
- **Threshold Sync**: Sync when pending changes exceed limit

## 4. UI/UX Design System

### 4.1 Design Tokens

#### Color Palette
```dart
class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF3A87AD);        // Blue-teal
  static const Color primaryLight = Color(0xFF5BA7C9);   // Light blue-teal

  // Surface colors
  static const Color surface = Color(0xFFFAFAFA);        // Light gray
  static const Color surfaceVariant = Color(0xFFF5F5F5); // Lighter gray

  // Text colors
  static const Color textPrimary = Color(0xFF2C3E50);    // Dark blue-gray
  static const Color textSecondary = Color(0xFF7F8C8D);  // Medium gray
  static const Color textLight = Color(0xFFFFFFFF);      // White

  // Status colors
  static const Color success = Color(0xFF27AE60);        // Green
  static const Color warning = Color(0xFFF39C12);        // Orange
  static const Color error = Color(0xFFE74C3C);          // Red
  static const Color info = Color(0xFF3498DB);           // Blue
}
```

#### Typography System
```dart
class DesignTokens {
  // Font sizes
  static double get fontSizeH1 => 20;      // Heading 1
  static double get fontSizeH2 => 17;      // Heading 2
  static double get fontSizeH3 => 15;      // Heading 3
  static double get fontSizeBodyLarge => 14;  // Body large
  static double get fontSizeBodyMedium => 12; // Body medium
  static double get fontSizeBodySmall => 10;  // Body small
  static double get fontSizeCaption => 8;     // Caption
}
```

#### Spacing System
```dart
class AppDimensions {
  // Spacing (4-point grid system)
  static double get spacing2 => 2.0;    // XXS
  static double get spacing4 => 4.0;    // XS
  static double get spacing8 => 8.0;    // SM
  static double get spacing12 => 12.0;  // MD
  static double get spacing16 => 16.0;  // LG
  static double get spacing20 => 20.0;  // XL
  static double get spacing24 => 24.0;  // XXL
  static double get spacing32 => 32.0;  // XXXL
}
```

### 4.2 Component Library

#### Core Components
- **AppButton**: Standardized button component
- **AppCard**: Consistent card layout
- **AppTextField**: Form input component
- **AppBadge**: Status indicator component
- **AppLoadingIndicator**: Loading state component
- **AppEmptyState**: Empty state component
- **AppErrorState**: Error state component

#### Layout Components
- **AppDivider**: Section divider
- **AppSectionHeader**: Section title component
- **AppListItem**: List item component

#### Feedback Components
- **Shimmer Loading**: Skeleton loading animations
- **SnackBar**: User feedback messages
- **Dialog**: Modal dialogs
- **Progress Indicators**: Loading progress

### 4.3 Responsive Design

#### Screen Size Adaptation
```dart
class AppDimensions {
  static double screenWidth = 360;
  static double screenHeight = 640;
  static double blockSizeHorizontal = 3.6;
  static double blockSizeVertical = 6.4;

  static void init(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    screenWidth = mediaQuery.size.width;
    screenHeight = mediaQuery.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;
  }

  static double getResponsiveFontSize(double fontSize) {
    return fontSize * (screenWidth / 360);
  }
}
```

#### Text Scaling
- **MediaQueryWrapper**: Controls text scaling
- **Responsive Typography**: Font sizes adapt to screen size
- **Accessibility Support**: Supports system text scaling preferences

### 4.4 Income Feature UI/UX Specifications

#### 4.4.1 Income Screen Layout and Structure

**Screen Architecture**: `income_screen.dart`
- **Layout Type**: CustomScrollView with SliverAppBar and SliverList
- **Scroll Behavior**: Infinite scroll with pagination (200px threshold)
- **Refresh Mechanism**: Pull-to-refresh with RefreshIndicator

**Screen Sections Hierarchy**:
```
CustomScrollView
├── SliverAppBar (floating: true, pinned: true)
│   ├── Title: "Income"
│   ├── DateRangeSelectorField (bottom section)
│   └── Add Button (actions)
├── Summary Section (SliverToBoxAdapter)
│   ├── Section Title: "Summary"
│   ├── Section Subtitle: "Overview of your income and forecasts"
│   ├── IncomeSummary Card
│   ├── MileageSummary Card
│   └── IncomeTrendsCard
├── History Header (SliverToBoxAdapter)
│   └── IncomeHistoryHeader
└── Paginated Income List (SliverList)
    ├── IncomeItemCard (repeated)
    └── Loading Indicator (when hasMorePages)
```

#### 4.4.2 Component Specifications

**SliverAppBar Configuration**:
- **Height**: 60px (PreferredSize)
- **Background Color**: `AppColors.primary`
- **Elevation**: 0
- **Title**: "Income" (white text)
- **Floating**: true, **Pinned**: true
- **Bottom Section**: DateRangeSelectorField with padding `EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 12.0)`
- **Actions**: Add IconButton with tooltip "Add new income record"

**Date Range Selector**:
- **Container**: White background with 30% opacity, 8px border radius
- **Field Type**: DateRangeSelectorField
- **Shimmer State**: ShimmerDateRangeSelector during loading
- **Error State**: Red container with error icon and message

**Summary Section Styling**:
- **Padding**: `EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0)`
- **Title**: "Summary" (titleMedium, bold)
- **Subtitle**: "Overview of your income and forecasts" (bodySmall, grey[600])
- **Card Spacing**: 16px between cards

#### 4.4.3 Income Summary Card

**IncomeSummary Component**:
- **Type**: ExpandableCard with animation
- **Animation**: 300ms fade-in with 50ms delay
- **Title**: "Income Summary"
- **Icon**: `Icons.account_balance_wallet`
- **Colors**: Primary theme colors
- **Border**: Primary color with alpha 30

**Content Layout**:
```
Row (mainAxisAlignment: spaceEvenly)
├── Total Income
│   ├── Icon: Icons.payments (18px, primary)
│   ├── Value: Formatted currency (titleSmall, bold, primary)
│   └── Label: "Total Income" (bodySmall, grey[600])
├── Average Income
│   ├── Icon: Icons.trending_up (18px, primary)
│   ├── Value: Calculated average (titleSmall, bold, primary)
│   └── Label: "Average" (bodySmall, grey[600])
└── Highest Income
    ├── Icon: Icons.emoji_events (18px, primary)
    ├── Value: Highest single income (titleSmall, bold, primary)
    └── Label: "Highest" (bodySmall, grey[600])
```

#### 4.4.4 Income Trends Chart

**IncomeTrendsCard Component**:
- **Type**: ExpandableCard with FL Chart integration
- **Animation**: Chart appears with 500ms opacity animation after 300ms delay
- **Title**: "Income Trends"
- **Icon**: `Icons.trending_up`
- **Colors**: Secondary theme colors

**Chart Configuration**:
- **Chart Type**: LineChart with dual lines (income + running average)
- **Height**: 250px
- **Margin**: 4px horizontal
- **Padding**: 8px vertical

**Chart Features**:
- **Toggle**: Group by week switch (top-right)
- **Grid**: Horizontal and vertical lines (grey.shade200, 0.8px)
- **Y-Axis**: 4 intervals, 35px reserved space, ultra-compact formatting
- **X-Axis**: Date labels (every 2nd), 25px reserved space
- **Lines**:
  - Primary line: Income data (AppColors.primary, 3px width, curved)
  - Secondary line: Running average (AppColors.secondary, 3px width, curved)
- **Fill**: Below-bar area with 10% alpha
- **Tooltips**: Black background (80% alpha), formatted currency values

**Weekly Grouping Logic**:
- **Start Day**: Monday (ISO 8601 standard)
- **Week Labels**: "W1: 28/04", "W2: 05/05" format
- **Tooltip Format**: "Week 1 (Mon 28/04 - Sun 04/05)"

#### 4.4.5 Income Item Card

**IncomeItemCard Component**:
- **Layout**: Card with InkWell for interactions
- **Margin**: `EdgeInsets.symmetric(horizontal: 16, vertical: 8)`
- **Border Radius**: 12px
- **Border**: Dynamic color based on income value (alpha 30)
- **Elevation**: 0

**Color Logic**:
- **Positive Income**: `AppColors.success`
- **Negative Income**: `AppColors.error`
- **Zero Income**: `Colors.grey`

**Card Structure**:
```
Padding (16px all)
├── Header Row
│   ├── Date Section
│   │   ├── Calendar Icon (16px, textSecondary)
│   │   └── Formatted Date (titleSmall, bold)
│   └── Mileage Section
│       ├── Speed Icon (14px, primary)
│       └── Mileage Text (bodyMedium, bold, primary)
├── Divider (12px spacing)
├── Income Section
│   ├── Wallet Icon (16px, dynamic color)
│   ├── "Net Income:" Label (bodyMedium, w500)
│   └── Formatted Amount (titleSmall, bold, dynamic color)
└── Balance Change Section
    ├── Progress Label Row
    │   ├── "Balance Change" (bodySmall, grey[600], w500)
    │   └── "Profit/Loss" (bodySmall, dynamic color, w600)
    ├── Linear Progress Indicator
    │   ├── Height: 4px
    │   ├── Border Radius: 2px
    │   └── Dynamic color based on profit/loss
    └── Initial/Final Balance Row
        ├── "Initial: [amount]" (bodySmall, grey[600])
        └── "Final: [amount]" (bodySmall, grey[600])
```

#### 4.4.6 Income Form Screen

**IncomeFormScreen Architecture**:
- **Base Class**: BaseFormScreen<Income>
- **Layout**: Scaffold with form sections
- **Validation**: Real-time form validation
- **Navigation**: MaterialPageRoute transition

**Form Sections**:

**1. Mileage Information Section**:
- **Title**: "Mileage Information" with speed icon
- **Fields**:
  - Initial Mileage: MileageInput (required)
  - Final Mileage: MileageInput (editing only, with validation)

**2. Initial Balance Section**:
- **Title**: "Initial Balance" with wallet icon
- **Fields** (all CurrencyInputField with allowNegative: true):
  - Initial Gopay
  - Initial BCA
  - Initial Cash
  - Initial OVO
  - Initial BRI
  - Initial Rekpon

**3. Final Balance Section** (editing only):
- **Title**: "Final Balance" with savings icon
- **Fields** (all CurrencyInputField with allowNegative: true):
  - Final Gopay
  - Final BCA
  - Final Cash
  - Final OVO
  - Final BRI
  - Final Rekpon

#### 4.4.7 Input Field Specifications

**CurrencyInputField**:
- **Prefix**: "Rp " (bold, theme color)
- **Border**: 10px radius, primary color with alpha 77
- **Focus Border**: Primary color, 1.5px width
- **Padding**: `EdgeInsets.symmetric(horizontal: 16, vertical: 14)`
- **Keyboard**: numberWithOptions (decimal: false, signed: true)
- **Input Formatters**: Allow negative decimals without decimal places
- **Validation**: Required fields show error messages

**MileageInput**:
- **Suffix**: "km" (bold, theme color)
- **Prefix Icon**: Speed icon (20px, primary with alpha 204)
- **Border**: Same as CurrencyInputField
- **Keyboard**: numberWithOptions (decimal: false, signed: true)
- **Input Formatters**: Allow negative integers only
- **Validation**: Custom validator for final > initial mileage

#### 4.4.8 State Management and UI States

**Loading States**:
- **Unified Shimmer**: IncomeUnifiedShimmerLoading for entire screen
- **List Shimmer**: IncomeListShimmerLoading for list only
- **Components**: ShimmerCard, ShimmerText, ShimmerContainer
- **Animation**: Smooth shimmer effect with proper spacing

**Empty State**:
- **Container**: 40% of screen height
- **Card**: Centered with 32px horizontal margin
- **Border**: Grey with alpha 30, 12px radius
- **Content**:
  - Wallet icon (48px, success color)
  - Title: "No Income Records Found" (titleMedium, bold)
  - Subtitle: "Add your first income record by tapping the Add button"
  - Add Button: Primary color with icon
  - Refresh hint: "Pull down to refresh" with refresh icon

**Error States**:
- **Container**: Red background with alpha 30, 8px radius
- **Content**: Error icon + error message
- **Padding**: 12px horizontal, 8px vertical

#### 4.4.9 User Interaction Flows

**Adding Income Record**:
1. Tap Add button in AppBar
2. Navigate to IncomeFormScreen
3. Fill required Initial Mileage
4. Fill Initial Balance fields
5. Submit form
6. Show success/error SnackBar
7. Return to Income screen with refresh

**Editing Income Record**:
1. Long press on IncomeItemCard
2. Show ItemActionsBottomSheet
3. Tap Edit action
4. Navigate to IncomeFormScreen (pre-filled)
5. Modify Final Mileage and Final Balance
6. Submit form
7. Show success/error SnackBar
8. Return to Income screen with refresh

**Viewing Income Details**:
1. Tap on IncomeItemCard
2. Show IncomeDetailsSheet (modal bottom sheet)
3. DraggableScrollableSheet (65% initial, 30% min, 95% max)
4. Detailed breakdown of all income components

**Deleting Income Record**:
1. Long press on IncomeItemCard
2. Show ItemActionsBottomSheet
3. Tap Delete action
4. Show confirmation AlertDialog
5. Confirm deletion
6. Show loading SnackBar
7. Delete record and refresh list
8. Show success/error SnackBar

#### 4.4.10 Charts and Visualization Details

**FL Chart Implementation**:
- **Line Charts**: Dual-line trend visualization (income + running average)
- **Interactive Features**: Touch tooltips with formatted data
- **Responsive Design**: Charts adapt to screen width
- **Animation**: Smooth 500ms opacity transitions

**Chart Styling**:
- **Grid Lines**: Grey.shade200, 0.8px stroke width
- **Chart Lines**: 3px width, curved, with area fill
- **Tooltips**: Black background (80% alpha), white text
- **Legend**: Circular indicators with labels
- **Axis Labels**: Compact formatting (1K, 10K, 100K)

**Weekly vs Daily Toggle**:
- **Switch**: Top-right of chart card
- **Active Color**: AppColors.primary
- **Weekly Logic**: Monday-start weeks with date range labels
- **Daily Logic**: Individual date points with running averages

#### 4.4.11 Income Details Sheet Specifications

**IncomeDetailsSheet Component**:
- **Type**: DraggableScrollableSheet modal
- **Initial Size**: 65% of screen height
- **Min Size**: 30% of screen height
- **Max Size**: 95% of screen height
- **Background**: Scaffold background with 16px top border radius

**Sheet Structure**:
```
Container
├── Drag Handle (40x4px, grey with alpha 80, 2px radius)
├── Header Section
│   ├── Wallet Icon + "Income Details" Title
│   └── Close Button (32x32px constraints)
├── Summary Card
│   ├── Calendar Icon + Formatted Date
│   ├── Net Income + Distance Summary Row
│   └── Dynamic border color based on income value
├── Mileage Section
│   ├── Speed Icon + "Mileage" Title
│   ├── Initial/Final Mileage Items
│   └── Total Distance (highlighted)
├── Initial Balance Section
│   ├── Wallet Icon + "Initial Balance" Title
│   ├── All 6 payment method balances
│   └── Total Initial (highlighted in blue)
├── Final Balance Section
│   ├── Wallet Icon + "Final Balance" Title
│   ├── All 6 payment method balances
│   └── Total Final (highlighted in purple)
└── Result Section
    ├── Trending Up Icon + "Result" Title
    └── Net Income (highlighted with dynamic color)
```

**Detail Item Styling**:
- **Icon Size**: 16px with dynamic colors
- **Label**: bodyMedium with w500 weight
- **Value**: bodyMedium with bold weight for totals
- **Spacing**: 8px vertical padding between items
- **Dividers**: Between sections and before totals

### 4.5 Navigation and Routing

#### Navigation Structure
- **Bottom Navigation**: Primary navigation with 4 tabs
- **Stack Navigation**: Screen-to-screen navigation
- **Modal Navigation**: Bottom sheets and dialogs

#### Route Management
```dart
class AppRoutes {
  static const String home = '/';
  static const String income = '/income';
  static const String orders = '/orders';
  static const String performance = '/performance';
  static const String spareparts = '/spareparts';
  static const String settings = '/settings';
}
```

#### Navigation Patterns
- **Tab Navigation**: Bottom navigation bar
- **Push Navigation**: Screen transitions
- **Modal Navigation**: Overlay screens
- **Deep Linking**: URL-based navigation support

## 5. Technical Implementation Details

### 5.1 Code Generation Setup

#### Build Configuration
```yaml
# build.yaml
targets:
  $default:
    builders:
      drift_dev:
        options:
          store_date_time_values_as_text: true
```

#### Code Generation Commands
```bash
# Generate all code
flutter packages pub run build_runner build

# Watch for changes and regenerate
flutter packages pub run build_runner watch

# Clean generated files
flutter packages pub run build_runner clean
```

#### Generated Files
- **Freezed Classes**: `*.freezed.dart` - Immutable data classes
- **JSON Serialization**: `*.g.dart` - JSON serialization methods
- **Riverpod Providers**: `*.g.dart` - Generated providers
- **Drift Database**: `*.g.dart` - Database access objects

### 5.2 Error Handling System

#### Failure Classes
```dart
@freezed
class Failure with _$Failure {
  const factory Failure.server([String? message]) = ServerFailure;
  const factory Failure.cache([String? message]) = CacheFailure;
  const factory Failure.network([String? message]) = NetworkFailure;
  const factory Failure.invalidInput([String? message]) = InvalidInputFailure;
  const factory Failure.database([String? message]) = DatabaseFailure;
  const factory Failure.businessLogic([String? message]) = BusinessLogicFailure;
  const factory Failure.notFound([String? message]) = NotFoundFailure;
  const factory Failure.unexpected([String? message]) = UnexpectedFailure;
}
```

#### Exception Handling
```dart
// Convert exceptions to failures
Failure exceptionToFailure(Exception exception) {
  if (exception is DatabaseException) {
    return Failure.database(exception.message);
  } else if (exception is NetworkException) {
    return Failure.network(exception.message);
  }
  // ... other exception types
  return Failure.unexpected(exception.toString());
}
```

### 5.3 Logging System

#### Sync Logger
```dart
class SyncLogger {
  void log(String message, {LogLevel level = LogLevel.info}) {
    final entry = LogEntry(
      timestamp: DateTime.now(),
      message: message,
      level: level,
    );

    _syncLogs.add(entry);
    _ref.read(syncLogsProvider.notifier).state = List.from(_syncLogs);
    debugPrint('SYNC: ${entry.formattedEntry}');
  }
}
```

#### Auth Logger
```dart
class AuthLogger {
  void addLogEntry(String message, {AuthLogImportance importance = AuthLogImportance.verbose}) {
    final entry = AuthLogEntry(
      timestamp: DateTime.now(),
      message: message,
      importance: importance,
    );

    _authLogs.add(entry);
    _ref.read(authLogsProvider.notifier).state = List.from(_authLogs);
    debugPrint('AUTH: ${entry.formattedEntry}');
  }
}
```

### 5.4 Repository Pattern

#### Base Repository Interface
```dart
abstract class SyncableRepository<T> {
  Future<int> add(T entity);
  Future<bool> update(T entity);
  Future<bool> delete(int id);
  Future<T?> getById(int id);
  Future<List<T>> getAll();
  Future<void> syncData();
}
```

#### Repository Implementation Pattern
```dart
class IncomeRepositoryImpl implements SyncableRepository<Income> {
  final AppDatabase _db;
  final Ref _ref;

  @override
  Future<int> add(Income entity) async {
    final id = await _db.into(_db.income).insert(
      _mapToCompanion(entity),
    );
    _triggerSync();
    return id;
  }

  void _triggerSync() {
    final syncService = _ref.read(syncServiceProvider);
    syncService.syncData(SyncOperation.upload);
  }
}
```

### 5.5 Provider Pattern with Riverpod

#### Provider Types Used
- **Provider**: Immutable data providers
- **StateProvider**: Mutable state providers
- **FutureProvider**: Asynchronous data providers
- **StreamProvider**: Stream-based providers
- **NotifierProvider**: Complex state management

#### Provider Examples
```dart
// Simple state provider
final syncStateProvider = StateProvider<AsyncValue<String>>((ref) {
  return const AsyncValue.data('synced');
});

// Future provider with auto-dispose
@riverpod
Future<LevelRequirements> levelRequirements(LevelRequirementsRef ref) async {
  final repository = ref.watch(levelRepositoryProvider);
  return await repository.getLevelRequirements();
}

// Stream provider for real-time data
final syncLogsProvider = StateProvider<List<LogEntry>>((ref) => []);
```

### 5.6 Form Validation

#### Validation Utilities
```dart
class FormValidators {
  static FormFieldValidator<String> required(String errorMessage) {
    return (String? value) {
      if (value == null || value.isEmpty) {
        return errorMessage;
      }
      return null;
    };
  }

  static FormFieldValidator<String> email([String? errorMessage]) {
    return (String? value) {
      if (value == null || value.isEmpty) return null;

      final RegExp emailRegex = RegExp(
        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
      );

      if (!emailRegex.hasMatch(value)) {
        return errorMessage ?? 'Enter a valid email address';
      }
      return null;
    };
  }
}
```

## 6. Build and Deployment

### 6.1 Build Configuration

#### Analysis Options
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    - camel_case_types
    - prefer_const_constructors
    - avoid_print
    - use_key_in_widget_constructors

analyzer:
  errors:
    missing_required_param: error
    missing_return: error
    todo: ignore
```

#### Environment Configuration
```dart
// .env file support
flutter_dotenv: ^5.1.0

// Environment variables
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

### 6.2 Platform-Specific Configuration

#### Android Configuration
- **Minimum SDK**: API 21 (Android 5.0)
- **Target SDK**: Latest stable
- **Permissions**: Internet, storage, network state

#### iOS Configuration
- **Minimum iOS**: 11.0
- **Permissions**: Network access
- **App Transport Security**: Configured for HTTPS

### 6.3 Performance Optimization

#### Database Optimization
- **Indexed Columns**: UUID, date, sync_status
- **Query Optimization**: Efficient queries with proper indexing
- **Batch Operations**: Bulk insert/update operations

#### UI Optimization
- **Lazy Loading**: Paginated data loading
- **Image Optimization**: Efficient image loading and caching
- **Widget Optimization**: Const constructors and efficient rebuilds

## 7. Testing Strategy

### 7.1 Testing Approach
- **Unit Tests**: Business logic and utilities
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end functionality
- **Repository Tests**: Data layer testing

### 7.2 Test Structure
```
test/
├── unit/
│   ├── core/
│   ├── features/
│   └── utils/
├── widget/
│   ├── components/
│   └── screens/
└── integration/
    ├── auth_flow_test.dart
    ├── sync_flow_test.dart
    └── data_flow_test.dart
```

## 8. Deployment and Distribution

### 8.1 Release Process
1. **Code Generation**: Run build_runner
2. **Testing**: Execute test suite
3. **Build**: Generate release builds
4. **Code Signing**: Sign applications
5. **Distribution**: Deploy to app stores

### 8.2 Version Management
- **Semantic Versioning**: MAJOR.MINOR.PATCH
- **Build Numbers**: Incremental build numbers
- **Release Notes**: Detailed change logs

## 9. Maintenance and Support

### 9.1 Monitoring
- **Crash Reporting**: Application crash tracking
- **Performance Monitoring**: App performance metrics
- **User Analytics**: Usage pattern analysis

### 9.2 Updates
- **Regular Updates**: Feature additions and bug fixes
- **Security Updates**: Security patch management
- **Dependency Updates**: Keep dependencies current

## 10. Conclusion

The drivly application represents a comprehensive solution for driver performance and income tracking. Built with modern Flutter architecture patterns, it provides robust offline functionality, cloud synchronization, and detailed analytics. The clean architecture ensures maintainability and scalability, while the comprehensive design system ensures consistent user experience.

This PRD serves as a complete blueprint for replicating the drivly application with identical functionality, architecture patterns, and code quality standards.
```
